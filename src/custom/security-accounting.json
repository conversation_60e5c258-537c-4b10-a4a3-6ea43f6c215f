{"HousemaidsPayrollBasicInfo": ["GET /accounting/HousemaidPayroll/", "POST /accounting/HousemaidPayroll/update", "GET /public/picklist/items/PAYROLL_TYPE"], "HousemaidsPayrollLoans": ["GET /accounting/HousemaidPayroll/", "GET /accounting/loans/loans/", "GET /accounting/loans/getHousemaidLoans/", "GET /accounting/Repayment/getHousemaidRepayments/", "GET /accounting/forgiveness/getHousemaidForgiveness/", "Delete /accounting/.*/delete/", "POST /accounting/loans/", "POST /accounting/forgiveness/", "POST /accounting/Repayment/", "POST /accounting/HousemaidPayroll/update"], "HousemaidsPayrollManagerNotes": ["GET /accounting/HousemaidPayroll/", "Delete /accounting/ManagerNotes/delete/", "POST /accounting/ManagerNotes/create", "POST /accounting/ManagerNotes/update", "GET /accounting/ManagerNotes/getHousemaidManagerNotes/", "GET /public/picklist/items/.*", "GET /public/download/.*"], "HousemaidsPayrollAnnualVacation": ["GET /accounting/HousemaidPayroll/", "Delete /accounting/ScheduledAnnualVacation/delete/", "POST /accounting/ScheduledAnnualVacation/create", "POST /accounting/ScheduledAnnualVacation/update", "GET /accounting/ScheduledAnnualVacation/getHousemaidAnnualVacation/", "GET /public/picklist/items/AnnualVacationType"], "HousemaidsPayrollComplaints": ["GET /accounting/HousemaidPayroll/", "GET /complaints/complaint/page/housemaidWithinPayroll/"], "BankStatementwire-transfer": ["GET /accounting/bankStatementFile/.*/summary", "POST /accounting/bankStatementFile/confirmTransactions", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/transactionsUnknownWireTransfers", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/transactionsUnknownCashDeposits", "GET /accounting/bankStatementFile/.*/cashAndWireTransactions", "POST /accounting/bankStatementFile/.*/cashAndWireTransactionsCSV", "GET /accounting/bankStatementFile/.*/getProcessSummary"], "BankStatementpdc-payments": ["GET /accounting/bankStatementFile/.*/summary", "POST /accounting/bankStatementFile/confirmTransactions", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/matchedPdcPaymentTransactions", "GET /accounting/bankStatementFile/.*/pdcPaymentTransactions", "POST /accounting/bankStatementFile/.*/pdcPaymentTransactionsCSV", "GET /accounting/bankStatementFile/.*/getProcessSummary"], "BankStatementdirect-debits": ["GET /accounting/bankStatementFile/.*/summary", "GET /accounting/bankStatementFile/.*/directDebitTransactions", "POST /accounting/bankStatementFile/confirmTransactions", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/directDebitMatchedTransactionReceivedPayment", "GET /accounting/bankStatementFile/.*/unMatchedDirectDebitTransactionsFromErp", "POST /accounting/bankStatementFile/confirmTransactions", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/directDebitMatchedTransactionBouncedPayment", "POST /accounting/bankStatementFile/resolveTransaction", "POST /accounting/bankStatementFile/addNoteToTransaction", "GET /accounting/payments/pdcByContract/", "POST /accounting/bankStatementFile/matchPaymentToTransaction", "GET /accounting/clients/clientcontracts/", "GET /accounting/payments/pdcByContract/", "GET /accounting/clients/clients/name", "POST /accounting/bankStatementFile/.*/directDebitTransactionsCSV", "POST /accounting/bankStatementFile/.*/unMatchedDirectDebitTransactionsFromErpCSV", "GET /accounting/bankStatementFile/.*/getProcessSummary"], "BankStatement-expenses": ["GET /accounting/bankStatementFile/.*/summary", "GET /accounting/officestaff/page/", "GET /staffmgmt/client/status/client", "GET /staffmgmt/client/status/prospect", "GET /accounting/contract/searchContract", "GET /accounting/freedomoperator/search/page", "GET /accounting/housemaid/searchHousemaid", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/clients/clients/name", "POST /accounting/bankStatementFile/confirmTransactions", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/matchedTransactionsClientRefunds", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/otherExpenses", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/bucketRefills", "POST /accounting/bankStatementFile/resolveTransaction", "GET /accounting/bankStatementFile/.*/expensesTransactions", "GET /accounting/bankStatementFile/.*/bankTransferTransactions", "POST /accounting/bankStatementFile/addNoteToTransaction", "POST /accounting/bankStatementFile/createTransaction/", "GET /accounting/payments/pdcByContract/", "POST /accounting/bankStatementFile/matchPaymentToTransaction", "GET /accounting/clients/clientcontracts/", "GET /accounting/payments/pdcByContract/", "POST /accounting/bankStatementFile/.*/expensesTransactionsCSV", "GET /accounting/bankStatementFile/.*/getProcessSummary", "GET /public/download/.*"], "BankStatementundefined-transactions": ["GET /accounting/bankStatementFile/.*/summary", "GET /accounting/officestaff/page/", "GET /staffmgmt/client/status/client", "GET /staffmgmt/client/status/prospect", "GET /accounting/freedomoperator/search/page", "GET /accounting/housemaid/searchHousemaid", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/expenses/page/searchExpenses", "POST /accounting/bankStatementFile/resolveTransaction", "GET /accounting/bankStatementFile/.*/undefinedTransactions", "POST /accounting/bankStatementFile/createTransaction/"], "BankStatement-summary": ["GET /accounting/bankStatementFile/.*/summary"], "BankStatementcheque-deposits": ["GET /accounting/bankStatementFile/.*/summary", "GET /accounting/clients/clients/name", "GET /accounting/bankStatementFile/.*/chequeDepositTransactions", "POST /accounting/bankStatementFile/confirmTransactions", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/matchedTransactionReceivedCheques", "GET /accounting/bankStatementFile/confirmAllTransactions/.*/matchedTransactionBouncedCheques", "POST /accounting/bankStatementFile/resolveTransaction", "POST /accounting/bankStatementFile/addNoteToTransaction", "GET /accounting/payments/pdcByContract/", "POST /accounting/bankStatementFile/matchPaymentToTransaction", "GET /accounting/clients/clientcontracts/", "POST /accounting/bankStatementFile/.*/chequeDepositTransactionsCSV", "GET /accounting/bankStatementFile/.*/getProcessSummary"], "BankStatementcredit-card-payments": ["GET /accounting/bankStatementFile/.*/summary", "POST /accounting/bankStatementFile/.*/confirmAndSaveTransactions", "GET /accounting/bankStatementFile/.*/getAddedTadbeerTransactions", "GET /accounting/bankStatementFile/.*/creditCardTransactions", "GET /accounting/bankStatementFile/.*/creditCardCommission", "GET /accounting/bankStatementFile/.*/creditCardAmount", "GET /accounting/revenues/page/searchRevenues", "POST /accounting/bankStatementFile/.*/creditCardTransactionsCSV", "POST /accounting/bankStatementFile/.*/getAddedTadbeerTransactionsCSV"], "ManageBuckets": ["POST /accounting/buckets/page/advancesearchbuckets", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/buckets/page/searchBuckets/csv", "POST /accounting/buckets/page/advancesearchbuckets/csv", "GET /accounting/buckets/getLastCalculateBucketAmountJobRun"], "accounting_BucketsForm": ["GET /accounting/buckets/", "GET /accounting/buckets/getRefillFrom", "POST /accounting/buckets/update", "POST /accounting/buckets/create", "GET /public/user/page"], "Revenues": ["GET /accounting/revenues/page/searchRevenues", "POST /accounting/revenues/update", "POST /accounting/revenues/create"], "multiple-dd-configuration": ["GET /accounting/ddfexportingconfig/page", "POST /accounting/ddfexportingconfig/update", "GET /accounting/direct-debit-config/page", "POST /accounting/direct-debit-config/update-all", "GET /accounting/direct-debit-config/search"], "send-manual-dd-to-bank": ["GET /accounting/manualddftosend/notsent", "POST /accounting/manualddftosend/confirmsenttobank/", "GET /public/download/.*"], "Expenses": ["GET /accounting/expenses/page/searchExpenses", "POST /accounting/expenses/deleteExpense/", "POST /accounting/expenses/enableDisableExpense/", "GET /accounting/expenses/page/searchExpenses/csv", "POST /accounting/expenses/update", "POST /accounting/expenses/create", "POST /accounting/expenses/page/searchExpensesNew", "GET /public/picklist/items/.*"], "Accommodation": ["GET /inexit/housemaid/costpermonth"], "ManageTransactions": ["GET /accounting/transactions/meta/transaction_management", "GET /accounting/transactions/page/advancesearch2", "GET /accounting/transactions/page/advancesearch2New", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/expenses/page/searchExpenses", "POST /transactions/csv/search/mail", "POST /accounting/transactions/csv/search", "POST /accounting/transactions/csv/advancesearch2/mail", "POST /accounting/transactions/csv/advancesearch2", "POST /accounting/transactions/page/advancesearch", "POST /accounting/transactions/page/advancesearchNew", "POST /accounting/transactions/page/search", "POST /accounting/transactions/page/searchNew", "Delete /accounting/transactions/delete/", "POST /accounting/transactions/csv/advancesearch/mail", "POST /accounting/transactions/csv/advancesearch", "GET /public/picklist/items/transaction_license", "GET /public/download/.*", "GET /accounting/payments/getallpaymentmethods"], "UnknownWireTransfer": ["POST /accounting/transactions/page/advancesearch", "POST /accounting/transactions/csv/advancesearch", "POST /accounting/transactions/csv/advancesearch/mail"], "UnknownMatchingWireTransfer": ["GET /accounting/clients/clients/name", "GET /accounting/buckets/page/searchBuckets", "GET /clientmgmt/contract/getunreplacedbouncedpayments/", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/wireTransferTempPayment/getPayments/", "Delete /accounting/wireTransferTempTransaction/delete/", "GET /accounting/clients/clientcontracts/", "POST /accounting/wireTransferTempPayment/savePayments", "GET /public/picklist/items/typeOfPayment", "GET /public/parameter"], "UnknownWireTransferConfirmTransactions": ["GET /accounting/clients/clients/name", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/wireTransferTempTransaction/getTransactions/", "POST /accounting/wireTransferTempTransaction/update", "POST /accounting/wireTransferTempPayment/confirmTransactions", "GET /public/picklist/items/transaction_license"], "CreateExpectedWireTransfer": ["GET /clientmgmt/client/activeAndPlannedRenewalClients", "GET /accounting/expectedwiretransfers/", "POST /accounting/expectedwiretransfers/create", "POST /accounting/expectedwiretransfers/update"], "EditExpectedWireTransfer": ["GET /clientmgmt/client/activeAndPlannedRenewalClients", "GET /accounting/expectedwiretransfers/", "POST /accounting/expectedwiretransfers/create", "POST /accounting/expectedwiretransfers/update"], "MatchingWireTransfer": ["GET /accounting/clients/clients/name", "GET /accounting/buckets/page/searchBuckets", "GET /clientmgmt/contract/getunreplacedbouncedpayments/", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/wireTransferTempPayment/getPayments/", "GET /accounting/wireTransferTempTransaction/delete/", "GET /accounting/clients/clientcontracts/", "POST /accounting/wireTransferTempPayment/savePayments", "POST /accounting/transactions/page/advancesearch", "GET /clientmgmt/client/activeAndPlannedRenewalClients"], "MatchingExpectedWireTransfer": ["GET /accounting/clients/clients/name", "GET /accounting/buckets/page/searchBuckets", "GET /clientmgmt/contract/getunreplacedbouncedpayments/", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/contract-payment-confirmation/", "GET /accounting/wireTransferTempPayment/getPayments/", "GET /accounting/clients/clientcontracts/", "POST /accounting/contract-payment-confirmation/confirm/wire-transfer/", "GET /public/picklist/items/.*", "GET /public/parameter"], "ExpectedWireTransferConfirmTransactions": ["GET /accounting/clients/clients/name", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/wireTransferTempTransaction/getTransactions/", "POST /accounting/wireTransferTempTransaction/update", "POST /accounting/wireTransferTempPayment/confirmTransactions", "GET /public/picklist/items/transaction_license"], "statementOfAccount": ["GET /accounting/buckets/page/searchBuckets", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/expenses/page/searchExpenses", "POST /accounting/account-balance/get-balance-report/*"], "AddEditTransaction": ["GET /accounting/payments/getallpaymentmethods", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/officestaff/page/", "GET /staffmgmt/officestaff/page/", "GET /accounting/housemaid/searchHousemaid", "GET /staffmgmt/client/status/client", "GET /recruitment/maidsAtCandidateWA/getCandidateByName", "GET /staffmgmt/client/status/prospect", "GET /accounting/contract/searchContract", "GET /accounting/freedomoperator/search/page", "GET /accounting/transactions/", "POST /accounting/transactions/update", "POST /accounting/transactions/create", "/accounting/transactions/file/", "Delete /accounting/transactions/delete/file/", "GET /public/picklist/items/transaction_license", "GET /public/download/.*"], "pdc-form": ["GET /accounting/nonclientpdc/", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/tenancyContracts/list", "POST /accounting/nonclientpdc/update", "POST /accounting/nonclientpdc/create"], "pdc-management": ["GET /accounting/expenses/page/searchExpenses", "POST /accounting/nonclientpdc/filter", "POST /accounting/nonclientpdc/filtersumpdcs", "DELETE /accounting/nonclientpdc/delete/", "GET /accounting/nonclientpdc/withdrawpdc/", "GET /accounting/nonclientpdc/bouncepdc/", "POST /accounting/nonclientpdc/update", "GET /public/download/.*"], "TenancyContracts": ["POST /accounting/tenancyContracts/advancedSearch/page", "GET /accounting/tenancyContracts/search/page", "DELETE /accounting/tenancyContracts/delete/", "POST /accounting/tenancyContracts/update", "GET /public/download/.*"], "TenancyContractsDetails": ["GET /accounting/tenancyContracts/", "POST /accounting/tenancyContracts/", "GET /accounting/tenancyContracts/update", "GET /accounting/tenancyContracts/create", "POST /accounting/tenancyContracts/update", "POST /accounting/tenancyContracts/create", "GET /accounting/nonclientpdc/byTenancyContract/"], "maids-cheques-mass-update": ["GET /accounting/payments/getallpaymentmethods", "POST /accounting/payments/page/advancesearch", "POST /accounting/payments/changepaymentstatus", "POST /accounting/payments/csv/advancesearch", "POST /accounting/payments/csv/advancesearch/mail", "GET /public/picklist/items/BankName"], "accounting_confirmed-vat": ["GET /accounting/vatrecords/meta/vatrecords_management_by_client", "GET /accounting/vatrecords/advancesearchbyclient/page", "GET /accounting/vatrecords/advancesearchbyclient/page", "POST /accounting/vatrecords/update*", "POST /accounting/vatrecords/close*"], "accounting_posting-engine": ["GET /accounting/transactionpostingrules/meta/transactionpostingrules_management", "GET /accounting/transactionpostingrules/advancesearch/page", "POST /accounting/transactionpostingrules/deactivate-rule/", "POST /accounting/transactionpostingrules/activate-rule/", "GET /accounting/transactionpostingrules/advancesearch/page", "Delete /accounting/transactionpostingrules/delete/"], "AddEditPostingRule": ["GET /accounting/paymentrequestpurposes/getallhousemaidspurpose", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/revenues/page/searchRevenues", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/payments/getallpaymentmethods", "POST /accounting/transactionpostingrules/create", "POST /accounting/transactionpostingrules/update", "GET /accounting/transactionpostingrules/", "GET /public/picklist/items/.*"], "accounting_client-payments-mass-update": ["DELETE /accounting/paymentsmatchingfiles/delete/", "GET /accounting/paymentsmatchingfiles/projectedpage", "POST /accounting/paymentsmatchingfiles/create", "GET /public/download/.*"], "accounting_client-payments-mass-update-details": ["GET /accounting/paymentsmatchingfiles/getrecords/by-status/", "GET /accounting/paymentsmatchingfiles/check-thread-status/", "POST /accounting/paymentsmatchingfiles/cancel-in-progress/", "POST /accounting/paymentsmatchingfiles/confirmpayments/", "POST /accounting/paymentsmatchingfiles/confirmallpayments/", "GET /accounting/paymentsmatchingfiles/getrecords/by-status/csv/"], "createMoneyRequests": ["GET /accounting/housemaid/searchHousemaid", "GET /accounting/officestaff/search/page", "GET /accounting/contract/page", "GET /accounting/moneyrequest/", "POST /accounting/moneyrequest/create*", "GET /public/picklist/items/.*"], "accounting_viewMoneyRequest": ["GET /accounting/housemaid/searchHousemaid", "GET /accounting/officestaff/search/page", "GET /accounting/contract/page", "GET /accounting/moneyrequest/", "POST /accounting/moneyrequest/create*", "GET /public/picklist/items/.*"], "moneyRequestsApproval": ["GET /accounting/moneyrequest/moneyrequestapproval", "GET /accounting/moneyrequest/approvedcashmoneyrequests", "GET /accounting/moneyrequest/approvedbanktransfermoneyrequests", "GET /accounting/moneyrequest/moneyrequestmanagerapproval", "GET /accounting/moneyrequest/editnotes/", "GET /accounting/moneyrequest/editmethod/", "GET /accounting/moneyrequest/editpurpose/", "GET /accounting/moneyrequest/paid/", "GET /accounting/moneyrequest/deny/", "GET /accounting/moneyrequest/approve/", "GET /accounting/moneyrequest/obtainmanagerapprove/", "GET /accounting/moneyrequest/getusersbyposition", "GET /accounting/moneyrequest/file/", "Delete /accounting/moneyrequest/delete/file/", "DELETE /accounting/moneyrequest/delete/", "GET /public/picklist/items/.*"], "approvedCashMoneyRequests": ["GET /accounting/moneyrequest/moneyrequestapproval", "GET /accounting/moneyrequest/approvedcashmoneyrequests", "GET /accounting/moneyrequest/approvedbanktransfermoneyrequests", "GET /accounting/moneyrequest/moneyrequestmanagerapproval", "GET /accounting/moneyrequest/editnotes/", "GET /accounting/moneyrequest/editmethod/", "GET /accounting/moneyrequest/editpurpose/", "GET /accounting/moneyrequest/paid/", "GET /accounting/moneyrequest/deny/", "GET /accounting/moneyrequest/approve/", "GET /accounting/moneyrequest/obtainmanagerapprove/", "GET /accounting/moneyrequest/getusersbyposition", "GET /accounting/moneyrequest/file/", "DELETE /accounting/moneyrequest/delete/file/", "DELETE /accounting/moneyrequest/delete/", "GET /public/picklist/items/.*"], "approvedBanktransforMoneyRequests": ["GET /accounting/moneyrequest/moneyrequestapproval", "GET /accounting/moneyrequest/approvedcashmoneyrequests", "GET /accounting/moneyrequest/approvedbanktransfermoneyrequests", "GET /accounting/moneyrequest/moneyrequestmanagerapproval", "GET /accounting/moneyrequest/editnotes/", "GET /accounting/moneyrequest/editmethod/", "GET /accounting/moneyrequest/editpurpose/", "GET /accounting/moneyrequest/paid/", "GET /accounting/moneyrequest/deny/", "GET /accounting/moneyrequest/approve/", "GET /accounting/moneyrequest/obtainmanagerapprove/", "GET /accounting/moneyrequest/getusersbyposition", "GET /accounting/moneyrequest/file/", "DELETE /accounting/moneyrequest/delete/file/", "DELETE /accounting/moneyrequest/delete/", "GET /public/picklist/items/.*"], "moneyRequestManagerApproval": ["GET /accounting/moneyrequest/moneyrequestapproval", "GET /accounting/moneyrequest/approvedcashmoneyrequests", "GET /accounting/moneyrequest/approvedbanktransfermoneyrequests", "GET /accounting/moneyrequest/moneyrequestmanagerapproval", "GET /accounting/moneyrequest/editnotes/", "GET /accounting/moneyrequest/editmethod/", "GET /accounting/moneyrequest/editpurpose/", "GET /accounting/moneyrequest/paid/", "GET /accounting/moneyrequest/deny/", "GET /accounting/moneyrequest/approve/", "GET /accounting/moneyrequest/obtainmanagerapprove/", "GET /accounting/moneyrequest/getusersbyposition", "GET /accounting/moneyrequest/file/", "Delete /accounting/moneyrequest/delete/file/", "DELETE /accounting/moneyrequest/delete/", "GET /public/picklist/items/.*"], "importCreditCardStatements": ["GET /accounting/CreditCardStatement/importStatements/", "GET /accounting/CreditCardStatement/insertNewRows/"], "matchTickets": ["GET /accounting/buckets/page/searchBuckets", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/revenues/page/searchRevenues", "POST /accounting/CreditCardStatement/GetTicketsAndPurchaseCards/", "GET /public/picklist/items/.*", "POST /accounting/creditcardstatement/dismisspurchasemultiple", "POST /accounting/creditcardstatement/dismissrefundsmultiple", "POST /accounting/creditcardstatement/matchmultipletickets"], "TransactionHistory": ["POST /accounting/transactions/transactionsHistory/"], "PaymentReport": ["GET /accounting/payments/getallpaymentmethods", "POST /accounting/payments/page/advancesearch", "POST /accounting/payments/csv/advancesearch", "POST /accounting/payments/csv/advancesearch/mail", "GET /public/picklist/items/.*", "POST /accounting/payments/calculatePaymentsSumAndVatByFiltered"], "VisaExpenses": ["GET /accounting/visarequestexpense/meta/visa-expenses", "GET /accounting/visarequestexpense/advanceSearch/page", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/visarequestexpense/page/search", "GET /accounting/visarequestexpense/dismiss*", "POST /accounting/visarequestexpense/list/dismiss*", "POST /accounting/visarequestexpense/list/addtransaction*", "POST /accounting/visarequestexpense/addtransaction*", "GET /accounting/visarequestexpense/advanceSearch/csv*", "GET /accounting/visarequestexpense/csv/search*", "GET /accounting/visaExpensePaymentTypeDetails/list*", "POST /accounting/visaExpensePaymentTypeDetails/updateAllRecords", "GET /accounting/visarequestexpense/getallpaymenttypes", "POST /accounting/visarequestexpense/getallpaymenttypes", "GET /accounting/visarequestexpense/getallexpensestatuses", "POST /accounting/visarequestexpense/getallexpensestatuses", "GET /accounting/visaExpenseConfiguration/getSearchInfo", "DELETE /accounting/visaExpensePaymentTypeDetails/delete/", "POST /accounting/visaExpensePaymentTypeDetails/create", "POST /accounting/visaExpenseConfiguration/create", "GET /accounting/visaExpenseConfiguration/page/search", "DELETE /accounting/visaExpenseConfiguration/delete/", "POST /accounting/visaExpenseConfiguration/update"], "TelecomManagement": ["GET /accounting/telecomphone/page/search*", "Delete /accounting/telecomphone/delete/", "POST /accounting/telecomphone/update*", "GET /accounting/telecomphone/search/csv"], "TelecomManagement_create_edit": ["GET /accounting/expenses/list*", "GET /accounting/telecomphone/*", "POST /accounting/telecomphone/update", "POST /accounting/telecomphone/create", "POST /accounting/telecomphone/addbill/", "GET /public/picklist/items/.*", "GET /public/download/.*"], "DewaReport": ["GET /accounting/dewa/getDEWA*", "GET /accounting/dewa/getDEWA/csv*"], "accounting_housemaidsRules": ["GET /accounting/housemaidrules/getalltypes", "GET /accounting/housemaidrules/.*/getall", "GET /accounting/housemaidrules/getsalarybreakdowns", "POST /accounting/housemaidrules/updatesalarybreakdowns/"], "accounting_insurance_invoicing": ["GET /accounting/insuranceagreement/debitnotelist", "GET /accounting/insuranceagreement/creditnotelist", "GET /accounting/insuranceagreement/debitnotelist/csv", "GET /accounting/insuranceagreement/creditnotelist/csv"], "accounting_insurance_agreements": ["GET /accounting/insuranceagreement/page", "POST /accounting/insuranceagreement/update", "POST /accounting/insuranceagreement/create"], "companies": ["GET /accounting/companies/page", "POST /accounting/companies/update", "POST /accounting/companies/create", "DELETE /accounting/companies/delete/", "POST /accounting/companies/update/", "GET /accounting/companies/validatereports"], "pnlreport_structure": ["GET /accounting/plcompanies/searchplcompanies/list", "GET /accounting/plcompanies/", "POST /accounting/plvariables/updatevariable/", "POST /accounting/plvariables/update/", "POST /accounting/plvariables/create", "POST /accounting/plnodes/updatevariable/", "POST /accounting/plnodes/update/", "POST /accounting/plnodes/create", "POST /accounting/plvariablebuckets/update", "DELETE /accounting/plnodes/delete/", "GET /accounting/plvariablebuckets/searchexpenseandrevenue", "POST /accounting/plvariablebuckets/getbucketUsage", "GET /accounting/plnodes/move/.*", "GET /accounting/plcompanies/validatereports", "GET /accountingop/plvariablebuckets/searchexpenseandrevenue"], "accounting_pnlreport_generate": ["GET /accounting/plcompanies/generatereport/"], "pnl_bucket": ["GET /accounting/plcompanies/list", "GET /accounting/plvariablebuckets/searchbyexpense/", "GET /accounting/plvariablebuckets/searchbyrevenue/", "GET /accounting/plcompanies/", "POST /accounting/plvariablebuckets/create*", "POST /accounting/plvariablebuckets/update*", "DELETE /accounting/plvariablebuckets/delete/"], "accounting_pnl_report": ["GET /accounting/plcompanies/searchplcompanies/list", "GET /accounting/plcompanies/downloadreport/", "GET /accounting/plcompanies/exporttransactionsbehindrow/csv/", "GET /accounting/plcompanies/showpagetransactionsbehindrow/"], "accounting_pnl_vs_system_code": ["GET /accounting/plcompanies/downloadreportvs/*"], "accounting_deposits": ["GET /accounting/payments/getallpaymentmethods", "GET /accounting/deposits/page/", "POST /accounting/deposits/update", "POST /accounting/deposits/create", "DELETE /accounting/deposits/delete/", "GET /accounting/deposits/file/*", "Delete /accounting/deposits/delete/file/"], "accounting_contract-payments": ["POST /accounting/contract-payment-confirmation/search", "DELETE /accounting/contract-payment-confirmation/deleteAll/", "DELETE /accounting/contract-payment-confirmation/deletedirectdebitpayments/", "GET /accounting/contractpayments/send/", "GET /accounting/contract-payment-confirmation/confirm/", "GET /accounting/expected-wire-transfer/matching-wire-transfer", "POST /accounting/contract-payment-confirmation/search/csv", "GET /public/download/.*"], "accounting_send-dd-to-bank": ["GET /accounting/directdebitfile/getBankNames", "POST /accounting/directdebitfile/advancesearch", "GET /accounting/directdebitfile/send/", "POST /accounting/directdebitfile/advancesearch/csv/sublist", "POST /accounting/directdebitfile/advancesearch/csv/alllist/send", "POST /accounting/directdebitfile/advancesearch/csv/alllist/download", "POST /accounting/directdebitfile/exportmultipleddfs", "POST /accounting/directdebitfile/generatebatch/rpa", "POST /accounting/directdebitfile/exportallddfs", "POST /accounting/directdebitfile/sendtobank", "GET /accounting/directdebitfile/downloadbatch", "GET /public/picklist/items/TypeOfPayment", "GET /public/download/.*", "GET /accounting/manualddftosend/notsent", "POST /accounting/manualddftosend/confirmsenttobank/", "GET /accounting/directdebitcancelationtodos/generateDDCancellation/", "POST /accounting/directdebitcancelationtodos/sentToBank", "POST /accounting/ddf_batch_for_rpa/advancesearch", "POST /accounting/directdebitfile/sendtobankbyrpa/", "POST /accounting/directdebitfile/sendtobankbyrpa/.*", "POST /visa/robotic-process/upload-batch/.*", "POST /accounting/ddf_batch_for_rpa/update", "GET /accounting/ddf_batch_for_rpa/.*", "GET /accounting/directdebitfile/advanceSearchForSendDDToBankAcc8544"], "accounting_dd-data-entry": ["POST /accounting/directdebitfile/advancesearch*", "GET /sales/ccserviceapplication/getnotapprovedapps*", "GET /sales/visaserviceapplication/getnotapprovedapps*", "POST /accounting/directDebit/updateDataEntryNotes/", "#sales#salesClientMobileNumberPopup", "GET /accounting/directdebitfile/advanceSearchForDDDataEntryAcc8544"], "accounting_confirm-payments": [], "accounting_automation-import-new-file": ["POST /accounting/bddactivationfiles/update", "POST /accounting/bankpaymentactivationfiles/update", "GET /accounting/bddactivationfiles/projectedlist", "GET /accounting/bankpaymentactivationfiles/projectedlist", "POST /accounting/bddactivationfiles/create", "POST /accounting/bankpaymentactivationfiles/create", "POST /accounting/bddactivationfiles/parse-file", "GET /public/download/.*"], "accounting_automation-importing-file-ddfile": ["GET /accounting/bddactivationfiles/meta/importing_bank_direct_debit_activation_file", "GET /accounting/bddactivationfiles/getrecords/", "GET /accounting/bddactivationfiles/getrecords/*", "POST /accounting/bddactivationfiles/confirmdd", "Delete /accounting/bddactivationrecords/delete/", "GET /accounting/bddactivationfiles/getrecords/csv/", "GET /accounting/directdebitfile/advancesearch2/", "GET /accounting/bddactivationfiles/matchdirectdebitfile/", "GET /accounting/bddactivationfiles/.*/getProcessSummary"], "accounting_automation-importing-file-paymentFile": ["GET /accounting/bankpaymentactivationfiles/getrecords/*", "POST /accounting/bankpaymentactivationfiles/confirmpayment/", "Delete /accounting/bankpaymentactivationrecords/delete/"], "accounting_direct-debit-applications": ["GET /accounting/directdebitfile/advancesearch2/", "POST /accounting/directdebitfile/update", "GET /accounting/directdebitfile/meta/directdebitfiles_management", "GET /accounting/directdebitfile/advancesearch2/page", "GET /accounting/directdebitfile/advancesearch2New/page", "GET /accounting/directdebitfile/advancesearch2/mail", "GET /accounting/directdebitfile/advancesearch2/csv", "GET /public/download/.*", "GET /accounting/directDebit/getalldirectdebitstatus.*"], "accounting_cancelFutrueDdsFromBank": ["GET /accounting/directdebitcancelationtodos/getAll", "GET /accounting/workflowTasks/getSearchbleFields", "POST /accounting/directdebitcancelationtodos/sentToBank", "POST /accounting/directdebitcancelationtodos/allSentToBank", "POST /accounting/directdebitfile/update", "GET /accounting/directdebitcancelationtodos/generateDDCancellation/", "GET /accounting/directdebitcancelationtodos/getpdfofimages/", "GET /accounting/directdebitcancelationtodos/getDirectDebitCancellationTempFile/", "GET /accounting/directdebitcancelationtodos/getAllDirectDebitCancellation", "POST /accounting/directdebitcancelationtodos/generateDDCancellation", "GET /accounting/directdebitcancelationtodos/getAllDirectDebitCancellationTempFile", "POST /accounting/directdebitcancelationtodos/getDirectDebitCancellationTempFile"], "accounting_bank-direct-debit-cancellation-file": ["GET /accounting/bddcancelationfiles/projectedlist", "POST /accounting/bddcancelationfiles/update", "POST /accounting/bddcancelationfiles/create", "GET /accounting/importing-bank-dd-cancellation-file/", "GET /public/picklist/items/managers", "GET /public/download/.*"], "accounting_importing-bank-direct-debit-cancellation-file": ["GET /accounting/bddcancelationfiles/getrecords/", "POST /accounting/bddcancelationfiles/confirmdd/", "POST /accounting/bddcancelationfiles/confirmAlldd", "GET /accounting/bddcancelationfiles/getrecords/csv/", "POST /accounting/bddcancelationrecords/bulkdelete", "POST /accounting/bddcancelationfiles/dismissAlldd", "POST /accounting/bddcancelationrecords/approveAllRejectionByBankForDDs", "POST /accounting/bddcancelationrecords/approveRejectionByBankForDDs"], "accounting_add_client_refund": ["GET /accounting/clients/clientsbymobile/", "GET /accounting/clients/page/", "GET /accounting/clients/clientinfo/", "GET /accounting/clients/clientcontractsWithStatus/", "GET /accounting/clients/clientcontracts/", "GET /accounting/clientRefundTodo/clientAccountInfo/", "GET /accounting/paymentrequestpurposes/getallclientspurposewithsetup", "POST /complaints/complaint/get-open-contract-complaints/.*/or-closed-since/7/days", "GET /accounting/clients/clientsbymobile/", "GET /accounting/clients/clientinfo/", "GET /accounting/contract/getUnusedDaysForCompensatingContract/", "GET /accounting/contractpaymentterm/getAmountAtTime/", "GET /accounting/contractpaymentterm/checkiban/", "POST /accounting/payments/search", "POST /accounting/clientRefundTodo/create", "GET /public/parameter.*", "GET /public/picklist/items/Partial_Refund_For_Cancellation_Payments_Method", "GET /public/picklist/items/Absconder_Maid_Refund_Options", "GET /payroll/HousemaidPayroll/.*/getPendingPayrollMonths/", "GET /accounting/payments/getLastPaymentPaidByPurpose/"], "accounting_add_maid_refund": ["GET /accounting/housemaid/searchActiveHousemaid", "POST /accounting/expenseRequestTodo/create", "GET /accounting/expenseRequestTodo/expensesavailableforpage", "GET /accounting/expenses/", "GET /accounting/housemaid/checktocreatevacationrequest", "GET /staffmgmt/HousemaidVacations/gethousemaidvacationsinfo/", "GET /public/parameter.*", "GET /public/picklist/items/Partial_Refund_For_Cancellation_Payments_Method", "GET /accounting/housemaid/getActiveMaids", "GET /payroll/ManagerNotes/getAdditionsForMaid", "GET /recruitment/housemaid/calculate-passed-days-with-client", "GET /public/download/.*"], "accounting_client-refund-summary": ["GET /complaints/category/list/withTypes", "GET /accounting/paymentrequestpurposes/getallclientspurposewithsetup", "POST /accounting/clientRefundTodo/search/page", "POST /accounting/clientRefundTodo/downloadSearchAttachment/csv*", "GET /public/picklist/items/REFUND_PURPOSE_CATEGORY", "GET /public/download/.*"], "accounting_threshold_table": ["GET /accounting/paymentrequestpurposes/page", "POST /accounting/paymentrequestpurposes/update", "POST /accounting/paymentrequestpurposes/create", "GET /accounting/paymentrequestpurposes/csv", "GET /public/picklist/items/.*", "GET /public/user/page/"], "accounting_payroll-test": ["GET /accounting/HousemaidPayroll/generatePayrollTest"], "accounting_public-holidays": ["GET /accounting/publicholidays/getbyyear/", "POST /accounting/publicholidays/update", "POST /accounting/publicholidays/create", "DELETE /accounting/publicholidays/delete/"], "accountingDirectDebitForm": ["GET /accounting/contractpaymentterm/getnewddInfo", "GET /accounting/contractpaymentterm/addnewmanualdd", "GET /clientmgmt/contract/getunreplacedbouncedpayments/", "GET /accounting/payments/getpaymentinfoforreplacement/", "GET /accounting/contractpaymentterm/getcontractpaymenttermbycontractwithdirectdebitpayments/", "POST /accounting/contractpaymentterm/add-multiple-dds/", "GET /accounting/contractpaymentterm/checkiban/", "POST /clientmgmt/clientdocuments/create", "GET /accounting/contractpaymentterm/hasapprovedddfile/", "GET /public/parameter.*", "GET /public/picklist/items/.*", "POST /public/upload", "GET /accounting/payments/getPaymentDiscountInfo/*"], "accountingDirectDebitPayments": ["GET /accounting/contractpaymentterm/getcontractpaymenttermbycontractwithdirectdebitpayments/", "POST /accounting/contractpaymentterm/updatecontractpaymenttermwithpayments*", "GET/accounting/contractpaymentterm/checkiban/", "POST /clientmgmt/clientdocuments/create", "GET /accounting/contractpaymentterm/hasapprovedddfile/", "GET /public/picklist/items/.*", "GET /public/parameter.*"], "accountingSignDD": ["GET /accounting/contractpaymentterm/getddsignlink/", "GET /accounting/contractpaymentterm/sendddmessagetoclient/", "GET /accounting/contractpaymentterm/checkifcontractneedsign/", "GET /accounting/contractpaymentterm/getcontractpaymenttermbycontractwithcashpayments/", "GET /accounting/contractpaymentterm/getddinfoforclient", "POST /accounting/contractpaymentterm/signddbyclient", "POST /accounting/contractpaymentterm/getnamefromeidphoto", "GET /public/picklist/items/typeOfPayment", "GET /public/download/.*"], "accountingPaymentsFiles": ["GET /accounting/contractpaymentterm/getddfiles/", "GET /accounting/contractpaymentterm/getmergedddfiles/", "POST /accounting/contractpaymentterm/update", "GET /accounting/contractpaymentterm/sendddfilestoclient/", "POST /accounting/directdebitfile/createAll", "POST /accounting/directdebitfile/update", "GET /accounting/contractpaymentterm/getpdfofimages/", "GET /public/download/.*"], "accountingContractCashPayments": ["GET /accounting/contractpaymentterm/getcontractpaymenttermbycontractwithcashpayments/", "POST /accounting/contractpayments/savepayments/", "GET /public/picklist/items/typeOfPayment"], "accountingConfirmDirectDebit": ["GET /accounting/directdebitfile/dd-data-entry/", "GET /accounting/directdebitfile/dd-data-entry-validateClient/", "GET /accounting/directDebit/confirmddbankinfo/", "GET /accounting/contractpaymentterm/checkiban/", "GET /accounting/directDebit/removeddbankphoto/", "POST /accounting/contractpaymentterm/swapBankInfoAttachments", "GET /public/picklist/items/.*"], "accountingConfirmDirectDebitAccountant": ["GET /accounting/directdebitfile/", "POST /accounting/directdebitrejectiontodos/complete/"], "accountingAutomatedDirectDebitMessages": ["GET /accounting/ddmsgconfig/list", "POST /accounting/ddmsgconfig/update"], "accounting_ddMessagingSetup": ["GET /accounting/DDMessaging/page", "POST /accounting/DDMessaging/advanceSearch", "POST /accounting/DDMessaging/update", "GET /public/picklist/items/BouncedPaymentStatus", "GET /accounting/DDMessaging/getDdMessagingEvent", "GET /accounting/DDMessaging/getDdMessagingSubEvent", "GET /accounting/DDMessaging/getDdMessagingPaymentStructure"], "accounting_ddMessageForm": ["GET /accounting/DDMessaging/", "POST /accounting/DDMessaging/update", "POST /accounting/DDMessaging/create", "POST /admin/template/update", "GET /public/picklist/items/BouncedPaymentStatus", "GET /accounting/ddBankMessaging/getDdBankMessaging/.*", "POST /accounting/ddBankMessaging/create", "POST /accounting/ddBankMessaging/update", "DELETE /accounting/ddBankMessaging/delete/", "GET /accounting/DDMessaging/getDdMessagingPaymentStructure", "GET /accounting/DDMessaging/getDdMessagingSubEvent", "POST /accounting/DDMessaging/updateTemplatesChannels/"], "accounting_UploadStatements": ["POST /accounting/bankStatementFile/create", "POST /accounting/bankStatementFile/deleteFileStatement", "GET /accounting/bankStatementFile/filterStatement", "GET /public/download/.*", "POST /public/upload"], "accounting_ExpenseCategory": ["GET /accounting/expenses/page/searchExpenses", "GET /accounting/supplier/update-sales-binder-data", "GET /accounting/category/page/", "POST /accounting/category/update", "GET /public/picklist/items/category_order_cycle"], "accounting_ExpenseItems": ["GET /accounting/category/page", "GET /accounting/item/page-with-search", "GET /accounting/category/", "GET /accounting/category/get-items/", "GET /accounting/item/page", "GET /accounting/supplier/update-sales-binder-data", "POST /accounting/item/calculate-initial-cycle-inventory/", "POST /accounting/item/update", "GET /public/picklist/items/item_measure_of_consumption"], "accounting_CreditCardHolder": ["GET /accounting/expense-payment/get-pending-credit-card-payments*"], "accounting_CreditCardHolderPayForm": ["GET /accounting/expense-payment/get-credit-card-buckets", "GET /accounting/expense-payment/", "POST /accounting/expense-payment/pay-credit-card-payment/", "GET /public/download/.*"], "accounting_PayInvoice": ["GET /accounting/expense-payment/get-pay-invoice-list"], "accounting_StockKeeperTodo": ["GET /accounting/stock-keeper/get-stock-keeper-tasks", "GET /accounting/stock-keeper/get-bill-info-for-maintenance-request/", "GET /accounting/stock-keeper/get-order-object/", "GET /accounting/stock-keeper/confirm-receive-order/", "POST /accounting/stock-keeper/purchase-in-one-bill/", "GET /accounting/stock-keeper/done-maintenance-request/"], "accounting_AuditManager": ["GET /accounting/buckets/page/searchBuckets", "GET /accounting/bucket-replenishment/getRequests", "GET /accounting/bucket-replenishment/approveRequest", "GET /accounting/bucket-replenishment/rejectRequest", "GET /accounting/bucket-replenishment/doneRequest", "POST /accounting/bucket-replenishment/addApprovedRequest/", "GET /accounting/reconciliator/getDoneByReconciliator", "GET /accounting/reconciliator/missingInvoiceDismissed", "GET /accounting/reconciliator/missingTaxInvoice", "GET /public/download/.*"], "accounting_AuditManagerHistory": ["GET /accounting/buckets/page/searchBuckets", "GET /accounting/bucket-replenishment/searchRequests"], "accounting_PayInvoicesPayForm": ["GET /accounting/expense-payment/get-pay-invoice/", "POST /accounting/expense-payment/pay-invoice*"], "accounting_SupplierList": ["GET /accounting/supplier/page/", "GET /accounting/supplier/update-sales-binder-data", "GET /accounting/buckets/page/searchBuckets/csv", "POST /accounting/buckets/page/advancesearchbuckets/csv", "GET /accounting/supplier/exportToCsv", "POST /accounting/supplier/update"], "accounting_OneTimeExpenseRequest": ["GET /accounting/supplier/page/", "GET /recruitment/maidsAtCandidateWA/getCandidateByName", "GET /accounting/housemaid/searchHousemaid", "GET /payroll/officestaff/asPicklist", "GET /accounting/expenses/", "GET /accounting/expenseRequestTodo/expensesavailableforpage", "POST /accounting/expenseRequestTodo/create", "GET /public/picklist/items/.*", "GET /public/user/page/"], "accounting_MaintenanceRequest": ["GET /accounting/supplier/page/", "GET /accounting/expenseRequestTodo/expensesavailableforpage", "GET /accounting/expenses/", "POST /accounting/maintenance-request/create"], "accounting_SupplierListForm": ["GET /accounting/supplier/", "GET /accounting/buckets/getRefillFrom", "POST /accounting/supplier/update", "POST /accounting/supplier/create", "GET /accounting/contractpaymentterm/checkiban/", "GET /public/user/page"], "accounting_ClientRefundSetup": ["GET /accounting/clientRefundSetup/list"], "accounting_ClientRefundSetupForm": ["GET /accounting/paymentrequestpurposes/getallclientspurpose", "GET /complaints/category/list/withTypes", "POST /accounting/clientRefundSetup/update", "POST /accounting/clientRefundSetup/create", "GET /accounting/clientRefundSetup/", "GET /public/picklist/items/.*", "GET /public/user/page"], "accounting_ClientRefundApprovals": ["GET /accounting/workflowTasks/lazyLoad", "POST /accounting/workflowTasks/all", "GET /accounting/workflowTasks/all", "GET /visa/workflowTasks/getAllSearchbleFields", "GET /accounting/workflowTasks/getSearchbleFields", "GET /accounting/clientRefundTodo/", "GET /accounting/clientRefundTodo/getClientRefundsPreviousRequests", "POST /accounting/clientRefundTodo/complete/.*/WAITING_MANAGER_APPROVAL", "GET /public/download/.*", "GET /accounting/paymentrequestpurposes/getallclientspurpose", "GET /accounting/clientRefundTodo/getAllowRefundAmount/", "GET /accounting/clientRefundTodo/clientRefundTodoAllowEdit", "GET /accounting/payments/getAllPaymentOfContractPaidByCard/", "GET /accounting/contractpaymentterm/checkiban/", "GET /public/picklist/items/.*", "GET /public/parameter.*", "GET /accounting/clientRefundTodo/getClientRefundInfo/", "POST /accounting/payments/search", "POST /accounting/clientRefundTodo/updateConditionalRefundTodo"], "accounting_ClientRefundCEOScreen": ["POST /accounting/clientRefundTodo/complete/.*/WAITING_COO_APPROVAL", "POST /accounting/coo-questions/send-rejection-mail", "GET /accounting/clientRefundTodo/getCeoScreenInfo", "GET /payroll/pendingApproval/getCEOApprovalRequests", "GET /accounting/expenseRequestTodo/getCooMaidAdditionRequests", "GET /accounting/expenseRequestTodo/approve-request", "GET /accounting/expenseRequestTodo/rejectrequest", "POST /accounting/coo-questions/send-rejection-mail", "GET /accounting/expenseRequestTodo/getbetterprice", "GET /accounting/expenseRequestTodo/getCooExpenseRequests", "GET /accounting/cooexpensenotification/allnotdismissed", "GET/accounting/cooexpensenotification/dismiss", "GET /accounting/clientRefundTodo/getCeoPendingToDos", "GET /payroll/public/approveFinalManager", "GET /payroll/finalSettlement/getFinalSettlement/", "POST /accounting/clientRefundTodo/ceoAction/approveAll", "POST /accounting/clientRefundTodo/ceoAction/approveList", "POST /accounting/expenseRequestTodo/approve-all-requests", "POST /accounting/expenseRequestTodo/approve-list-requests", "POST /payroll/pendingApproval/bulkCEOApproveRequests", "POST /accounting/coo-questions/create", "POST /payroll/finalSettlement/approveFinalSettlement", "GET /recruitment/caller/action-need-coo-approval/get-list", "POST /recruitment/caller/action-need-coo-approval/approve-all", "POST /recruitment/caller/action-need-coo-approval/approve-many", "POST /recruitment/caller/action-need-coo-approval/Approved", "POST /recruitment/caller/action-need-coo-approval/Rejected", "GET /freedomop/soaManualRecord/getFreedomOperatorsManualRequests", "GET /freedomop/soaManualRecord/approveAllManualRecords", "POST /freedomop/soaManualRecord/approveListManualRecords", "GET /freedomop/soaManualRecord/approveOneManualRecords/", "POST /accounting/coo-questions/send-rejection-mail", "GET /public/user/page.*", "GET /public/download/.*"], "accounting_AccountantTodoList": ["GET /accounting/accountantTodo/findOpenedTodos", "POST /accounting/accountantTodo/accountantAction", "GET /accounting/accountantTodo/cancelOpenedTodo/", "POST /accounting/accountantTodo/findOpenedTodos/csv", "POST /accounting/accountantTodo/findOpenedTodos/csv/mail"], "accounting_BankTransferConfirmation": ["GET /accounting/accountantTodo/findPendingConfirmationTodos", "POST /accounting/accountantTodo/managerAction", "POST /accounting/accountantTodo/ceoAction", "POST /accounting/coo-questions/send-rejection-mail", "POST /accounting/accountantTodo/ceoAction/approveAll", "POST /accounting/accountantTodo/ceoAction/approveList", "POST /accounting/coo-questions/create", "GET /public/user/page/.*"], "accounting__OTPAuthRequired": [], "accounting_SendOTP": [], "AddClientPaymentsForApproval": ["GET /accounting/contract/getIntialandProratedAllowedPayments/", "GET /clientmgmt/contract/getunreplacedbouncedpayments/", "GET /clientmgmt/contract/", "GET /accounting/payments/", "GET /clientmgmt/client/", "GET /accounting/contract-payment-confirmation/allowAddDiscountToPaymentForApproval", "GET /accounting/contract-payment-confirmation/getPayingViaCreditCardPayment/", "POST /accounting/contract-payment-confirmation/erp/create", "POST /accounting/contract-payment-confirmation/AddPaymentForApprovalInfo/", "POST /accounting/contract-payment-confirmation/checkIfUserForgetToMarkPaymentAsProrated/", "POST /accounting/payments/search", "GET /public/parameter", "GET /public/picklist/items/.*", "GET /accounting/payments/getPaymentDiscountInfo/*", "POST /accounting/contract-payment-confirmation/validateCreateToDoFromErp", "GET /accounting/contract-payment-confirmation/sendPayTabsLinkViaMessage", "GET /accounting/contract-payment-confirmation/setReactivationPaymentForTodo/", "GET /sales/clientenchantertodo/getaccountingclientenchantertodoextrainfo", "GET /accounting/contract-payment-confirmation/sendPayTabsLinkViaMessage", "GET /accounting/contract-payment-confirmation/checkMatchedToDoIfRelatedToRunningFlow/", "POST /accounting/contract-payment-confirmation/proceedStopRelatedFlowAndCreateToDo/", "POST /accounting/contract-payment-confirmation/checkForExistingTokenizedPayments"], "ACCOUNTING__ExpenseSetup": ["GET /accounting/expenses/", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/supplier/search/page/", "GET /accounting/expenses/getSubExpenseCode/", "POST /accounting/expenses/update", "POST /accounting/expenses/create", "GET /public/picklist/items/.*", "GET /public/parameter", "GET /accounting/supplier/getActiveSuppliers", "GET /public/user/page/.*"], "ACCOUNTING__CashierScreen": ["GET /accounting/expense-payment/get-pending-invoice-payments", "GET /accounting/expense-payment/get-current-balance", "GET /accounting/expense-payment/get-pending-cashier-payments", "POST /accounting/cash-box/update-cash-box", "GET /accounting/paymentOrders/getFinalSettlementPayments", "POST /accounting/paymentOrders/collect/", "POST /accounting/paymentOrders/reject/", "POST /public/upload", "POST /public/download/.*"], "ACCOUNTING__CashierPay": ["GET /accounting/expense-payment/", "POST /accounting/expense-payment/cashier-pay-money/", "POST /accounting/expense-payment/cashier-collect-money/", "POST /public/download/.*", "POST /public/upload"], "ACCOUNTING__CashierCollectInvoice": ["GET /accounting/expense-payment/", "POST /accounting/xpense-payment/cashier-collect-invoice/", "POST /public/download/.*"], "ACCOUNTING__ExpensesRequests": ["GET /accounting/expenses/page/searchExpenses", "GET /accounting/housemaid/searchHousemaid", "GET /accounting/officestaff/search/page", "GET /recruitment/maidsAtCandidateWA/getCandidateByName", "POST /accounting/expenseRequestTodo/search", "GET /accounting/expense-payment/", "GET /accounting/expenseRequestTodo/addrefund", "GET /public/user/page/.*", "POST /public/download/.*", "GET /accounting/supplier/page.*", "GET /accounting/expense-payment/getExpensePayment/", "POST /recruitment/booker/ticket-available", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/expenseRequestTodo/getPendingForApprovalUsersEmails", "POST /accounting/expenseRequestTodo/deleteEntityWithNotes/", "POST /accounting/expenseRequestTodo/search/csv", "POST /accounting/expenseRequestTodo/search/csv/mail"], "ACCOUNTING__AddExpenseRequest": ["GET /recruitment/maidsAtCandidateWA/getCandidateByName", "GET /accounting/supplier/search/page/", "GET /accounting/housemaid/searchHousemaid", "GET /payroll/officestaff/asPicklist", "GET /accounting/expenses/", "GET /accounting/supplier/", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/expenseRequestTodo/expensesavailableforpage", "POST /accounting/expenseRequestTodo/create", "GET /accounting/contractpaymentterm/checkiban/", "POST /accounting/expenses/add-supplier/", "GET /public/picklist/items/.*", "GET /public/user/page/"], "ACCOUNTING__AddVIPExpenseRequest": ["GET /accounting/buckets/page/searchBuckets", "GET /recruitment/maidsAtCandidateWA/getCandidateByName", "GET /accounting/supplier/search/page/", "GET /accounting/housemaid/searchHousemaid", "GET /payroll/officestaff/asPicklist", "GET /accounting/expenses/", "GET /accounting/expenseRequestTodo/expensesavailableforpage", "POST /accounting/expenseRequestTodo/create", "GET /accounting/contractpaymentterm/checkiban/", "POST /accounting/expenses/add-supplier/", "GET /public/picklist/items/.*", "GET /public/user/page/"], "ACCOUNTING__RequestExpense": ["GET /accounting/expenseRequestTodo/expensesavailableforpage"], "ACCOUNTING__RequestTransportationExpenses": ["GET /accounting/expenses/getByCode", "GET /accounting/expenseRequestTodo/transportationexpenserequestneedsinvoice", "POST /accounting/expenseRequestTodo/parsetransportationexpensefile", "POST /accounting/expenseRequestTodo/create", "GET /accounting/supplier/", "POST /accounting/expenses/add-supplier/", "GET /accounting/contractpaymentterm/checkiban/", "POST /accounting/expenseRequestTodo/TransportationExpenseGenerateCSV", "GET /public/user/page/", "GET /public/parameter", "GET /public/download/.*"], "ACCOUNTING__RequestDewaExpenses": ["GET /accounting/expenseRequestTodo/getAvailableDewaPhones", "GET /accounting/expenses/getByCode", "POST /accounting/expenseRequestTodo/create", "GET /public/parameter"], "ACCOUNTING__RequestTelecomExpenses": ["GET /accounting/expenseRequestTodo/getAvailableTelecomPhonesUsages", "GET /accounting/expenses/getByCode", "GET /accounting/expenseRequestTodo/getAvailableTelecomPhonesExpenses", "POST /accounting/expenseRequestTodo/create", "GET /public/parameter"], "ACCOUNTING__RequestTicketingExpenses": ["GET /recruitment/maidsAtCandidateWA/getCandidateByName", "GET /accounting/housemaid/searchHousemaid", "GET /payroll/officestaff/asPicklist", "GET /accounting/expenseRequestTodo/expensesavailableforpage", "GET /accounting/expenses/", "POST /accounting/expenseRequestTodo/create", "POST /accounting/expenses/add-supplier/", "GET /accounting/contractpaymentterm/checkiban/", "GET /public/user/page", "GET /public/picklist/items/.*", "GET /admin/picklist/items/.*"], "ACCOUNTING__RequestInsuranceExpenses": ["GET /accounting/expenses/getByCode", "GET /accounting/supplier/", "POST /accounting/expenseRequestTodo/create", "GET /public/parameter"], "ACCOUNTING__Reconciliator": ["GET /accounting/reconciliator/get-today-cash-box", "GET /accounting/reconciliator/get-complete-expenses-pending-confirmation", "GET /accounting/reconciliator/get-expenses-pending-invoices", "GET /accounting/CreditCardReconciliation/getByCreditCard", "POST /accounting/CreditCardReconciliation/deleteStatement/", "GET /accounting/buckets/page/searchBuckets", "POST /accounting/CreditCardReconciliation/uploadStatement/", "GET /public/download/.*", "POST /accounting/transactions/page/advancesearch3", "GET /admin/picklist/items/"], "ACCOUNTING__ReconciliatorConfirmPayment": ["GET /accounting/buckets/page/searchBuckets", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/expense-payment/", "POST /accounting/reconciliator/select-as-invoice/", "POST /accounting/reconciliator/select-as-vat-invoice/", "POST /accounting/reconciliator/add-invoice/", "POST /accounting/reconciliator/delete-invoice/", "GET /accounting/supplier/", "POST /accounting/expense-payment/saveTask/", "POST /accounting/expense-payment/complete/", "GET /public/download/.*", "GET /public/picklist/items/EXPENSE_CURRENCY"], "ACCOUNTING__ReconciliatorCreditCardStatementDetails": ["GET /accounting/expenses/page/searchExpenses", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/CreditCardReconciliation/getDetails/", "GET /accounting/CreditCardReconciliation/", "POST /accounting/CreditCardReconciliation/wrongMatch/", "POST /accounting/CreditCardReconciliation/confirmRefund/", "POST /accounting/CreditCardReconciliation/confirmExpense/", "POST /accounting/CreditCardReconciliation/confirmReplenishment/", "GET /accounting/bucket-replenishment/", "GET /accounting/expenseRequestTodo/", "POST /accounting/CreditCardReconciliation/matchWithExpense/", "POST /accounting/CreditCardReconciliation/create-manual-transaction/", "POST /accounting/expenseRequestTodo/search", "POST /accounting/CreditCardReconciliation/confirm-statement/", "POST /accounting/reconciliation-transaction/update", "DELETE /accounting/reconciliation-transaction/delete/file/", "GET /accounting/CreditCardReconciliation/details/.*/alreadyMatchedTransactions", "POST /accounting/CreditCardReconciliation/details/.*/confirmAlreadyMatched", "GET /public/picklist/items/.*", "GET /public/parameter", "GET /public/download/.*"], "ACCOUNTING__ExpensesApprovals": ["GET /accounting/expenseRequestTodo/getApprovalExpenseRequests", "GET /accounting/expenseRequestTodo/getBeneficiaryExpenseRequestsHistory", "GET /accounting/expenseRequestTodo/getRelatedToExpenseRequestsHistory", "GET /accounting/expenseRequestTodo/approve-request", "GET /accounting/expenseRequestTodo/rejectrequest"], "ACCOUNTING__PurchaseRequests": ["GET /accounting/purchasing-auditor/get-auditor-requests-list"], "ACCOUNTING__PurchaseRequestsConfirmPurchaseRequestDetails": ["GET /accounting/purchasing-manager/get-purchase-orders-order-cycle-name/", "GET /accounting/purchasing/", "GET /accounting/purchasing-auditor/get-confirm-request-items/", "POST /accounting/purchasing/complete/", "POST /accounting/purchasing-auditor/update-consumption-rate/", "DELETE /accounting/purchase-item/delete/"], "ACCOUNTING__PurchaseRequestsConfirmPriceDetails": ["GET /accounting/purchasing-manager/get-purchase-orders-order-cycle-name/", "GET /accounting/purchasing/", "GET /accounting/purchasing-auditor/get-confirm-prices-supplier-grids/", "GET /accounting/purchasing-auditor/get-confirm-prices-review-grids/", "POST /accounting/purchasing-auditor/confirm-price-action-cancel/", "POST /accounting/purchasing-auditor/confirm-price-action-approve/", "POST /accounting/purchasing-auditor/confirm-price-action-get-better-prices/", "POST /accounting/purchasing-auditor/confirm-price-action-undo/", "POST /accounting/purchasing/complete/"], "ACCOUNTING__PurchaseRequestsConfirmMaintenanceRequestPricesDetails": ["GET /accounting/maintenance-request/", "POST /accounting/maintenance-request/complete/"], "ACCOUNTING__PurchasingManager": ["GET /accounting/purchasing-manager/get-manager-requests-list"], "ACCOUNTING__PurchasingManagerGetBestPrices": ["GET /accounting/purchasing-manager/get-purchase-orders-order-cycle-name/", "GET /accounting/purchasing-manager/get-suppliers-request-items/", "GET /accounting/supplier/get-salesbinder-supplier-list", "POST /accounting/purchasing-manager/approve-best-supplier-step/"], "ACCOUNTING__PurchasingManagerRecheckForBetterPriceDetails": ["GET /accounting/purchasing-manager/get-purchase-orders-order-cycle-name/", "GET /accounting/purchasing-manager/get-recheck-suppliers-request-items/", "GET /accounting/supplier/get-salesbinder-supplier-list", "POST /accounting/purchasing-manager/approve-get-better-supplier-step/"], "ACCOUNTING__PurchasingManagerMaintenanceRequestGetPrices": ["GET /accounting/supplier/search/page", "GET /accounting/maintenance-request/", "GET /accounting/expenses/", "POST /accounting/maintenance-request/complete/"], "ACCOUNTING__PurchasingManagerMaintenanceRequestGetBetterPrices": ["GET /accounting/supplier/page/", "GET /accounting/maintenance-request/", "GET /accounting/expenses/", "POST /accounting/maintenance-request/complete/"], "ACCOUNTING__PurchasingManagerPurchaseItems": ["GET /accounting/purchasing/", "GET /accounting/purchasing-manager/get-purchase-orders-order-cycle-name/", "GET /accounting/purchasing-manager/get-purchase-orders-to-purchase-step/", "POST /accounting/purchasing-manager/cancel-item-form-purchase-order-in-purchase-step/", "GET /accounting/purchasing-manager/get-bill-info-for-purchase/", "POST /accounting/purchasing-manager/purchase-in-one-bill/"], "ACCOUNTING__InsuranceAuditing": ["GET /accounting/InsuranceAuditingStatement/page", "DELETE /accounting/InsuranceAuditingStatement/", "GET /public/download/.*"], "ACCOUNTING__InsuranceAuditingSetup": ["GET /accounting/InsuranceAuditingSetup/list", "POST /accounting/InsuranceAuditingSetup/update", "POST /accounting/InsuranceAuditingSetup/create"], "ACCOUNTING__InsuranceStatementAuditing": ["GET /accounting/InsuranceAuditingStatement/uploadStatement", "GET /accounting/InsuranceAuditingSetup/list", "GET /accounting/InsuranceAuditingStatement/updateBalance", "GET /accounting/InsuranceAuditingStatement/.*/unmatchedRecords/csv", "GET /accounting/InsuranceAuditingStatement/.*/matchedRecords/csv"], "ACCOUNTING__PurchaseOrderHistory": ["GET /accounting/purchase-order/page", "GET /accounting/supplier/get-supplier-list", "GET /accounting/category/list", "GET /accounting/purchase-order/page-order", "GET /accounting/purchase-order/get-pending-purchase-request-list", "GET /accounting/purchase-order/get-items-of-order/", "GET /accounting/expense-payment/get-modified-by-status-method-date", "GET /accounting/expense-payment/get-modified-by-status-method-date/questioned-expenses", "GET /accounting/accountantTodo/get-modified-confirmed-todos", "GET /accounting/accountantTodo/get-modified-confirmed-todos/questioned-expenses", "GET /accounting/reconciliator/get-reconciliator-actions/", "GET /accounting/reconciliator/get-reconciliator-actions/questioned-expenses", "GET /accounting/transactions/get-manual-transaction-by-date", "GET /accounting/transactions/get-manual-transaction-by-date/questioned-expenses", "POST /accounting/coo-questions/create", "POST /accounting/transactions/mark-as-done-by-coo", "POST /accounting/expense-payment/mark-as-done-by-coo", "POST /accounting/transactions/mark-all-as-done-by-coo", "POST /accounting/transactions/expense-payment/mark-list-as-done-by-coo", "GET /accounting/coo-questions/", "POST /accounting/coo-questions/answer-question/", "GET /public/user/page/.*", "GET /public/download/.*"], "ACCOUNTING__MaidExpensesSummary": ["GET /accounting/expenses/page/searchExpenses", "POST /accounting/expenses/search", "GET /accounting/housemaid/searchActiveHousemaid", "POST /accounting/expenseRequestTodo/search", "GET /accounting/expense-payment/", "GET /accounting/expenseRequestTodo/addrefund", "GET /public/user/page/.*", "GET /public/download/.*"], "ACCOUNTING__COOControl": ["GET /accounting/accountantTodo/findPendingConfirmationTodos", "POST /accounting/accountantTodo/managerAction", "POST /accounting/accountantTodo/ceoAction", "POST /accounting/coo-questions/send-rejection-mail", "POST /accounting/accountantTodo/ceoAction/approveAll", "POST /accounting/accountantTodo/ceoAction/approveList", "POST /accounting/coo-questions/create", "POST /accounting/clientRefundTodo/complete/.*/WAITING_COO_APPROVAL", "GET /accounting/expense-payment/get-modified-by-status-method-date", "GET /accounting/expense-payment/get-modified-by-status-method-date/questioned-expenses", "GET /accounting/accountantTodo/get-modified-confirmed-todos", "GET /accounting/accountantTodo/get-modified-confirmed-todos/questioned-expenses", "GET /accounting/reconciliator/get-reconciliator-actions", "GET /accounting/reconciliator/get-reconciliator-actions/questioned-expenses", "GET /accounting/transactions/get-manual-transaction-by-date", "GET /accounting/transactions/get-manual-transaction-by-date/questioned-expenses", "POST /accounting/coo-questions/create", "POST /accounting/transactions/mark-as-done-by-coo", "POST /accounting/expense-payment/mark-as-done-by-coo", "POST /accounting/transactions/mark-all-as-done-by-coo", "POST /accounting/expense-payment/mark-list-as-done-by-coo", "GET /public/user/page/.*", "GET /public/download/.*", "GET /public/parameter.*", "GET /accounting/clientRefundTodo/getCeoScreenInfo", "GET /accounting/expenseRequestTodo/getCooMaidAdditionRequests", "GET /accounting/expenseRequestTodo/getCooExpenseRequests", "GET /accounting/cooexpensenotification/allnotdismissed", "GET /accounting/clientRefundTodo/getCeoPendingToDos", "GET /freedomop/soaManualRecord/getFreedomOperatorsManualRequests", "GET /accounting/bankTransfers/coo-review", "GET /accounting/nonclientpdc/getPDCsByDueDate", "GET /accounting/nonclientpdc/mark-all-as-done-by-coo", "POST /accounting/nonclientpdc/mark-list-as-done-by-coo", "GET /payroll/pendingApproval/getCEOApprovalRequests", "GET /accounting/expenseRequestTodo/rejectrequest", "GET /accounting/expenseRequestTodo/approve-request", "GET /accounting/expenseRequestTodo/getbetterprice", "GET /accounting/cooexpensenotification/dismiss", "GET /payroll/public/approveFinalManager", "GET /payroll/finalSettlement/getFinalSettlement/", "POST /accounting/clientRefundTodo/ceoAction/approveAll", "POST /accounting/clientRefundTodo/ceoAction/approveList", "POST /accounting/expenseRequestTodo/approve-all-requests", "POST /accounting/expenseRequestTodo/approve-list-requests", "POST /recruitment/caller/action-need-coo-approval/approve-all", "POST /payroll/pendingApproval/bulkCEOApproveRequests", "POST /payroll/finalSettlement/approveFinalSettlement", "GET /recruitment/caller/action-need-coo-approval/get-list", "POST /recruitment/caller/action-need-coo-approval/approve-many", "POST /recruitment/caller/action-need-coo-approval/.*", "GET /freedomop/soaManualRecord/approveAllManualRecords", "POST /freedomop/soaManualRecord/approveListManualRecords", "GET /freedomop/soaManualRecord/approveOneManualRecords/", "GET /accounting/cooNightReviewTodo/getCooNightReview", "GET /accounting/cooNightReviewTodo/mark-all-as-done-by-coo", "POST /accounting/cooNightReviewTodo/mark-list-as-done-by-coo", "POST /accounting/bankTransfers/saveTask/.*", "POST /accounting/bankTransfers/complete/.*", "POST /accounting/bankTransfers/completeBulk/.*"], "ACCOUNTING__COOControlFreedomRequest": ["GET /freedomop/soaManualRecord/getFreedomOperatorsManualRequestDetails/", "GET /freedomop/soaManualRecord/approveOneManualRecords/", "POST /accounting/coo-questions/send-rejection-mail", "POST /accounting/coo-questions/create", "GET /public/user/page/"], "ACCOUNTING__PaymentPlansDataEntry": ["GET /accounting/contract/contractsForInfo", "GET /accounting/contract/contractPaymentPlan/", "POST /accounting/contract/submitContractInfo"], "ACCOUNTING__FailedDDsGeneration": ["GET /accounting/DirectDebitGenerationPlan/getfailedddsgeneration", "POST /accounting/DirectDebitGenerationPlan/generatepostponedds", "DELETE /accounting/DirectDebitGenerationPlan/delete/"], "ClientTaxiWorkOrderBills": [], "visa_DDRPAController": ["POST /visa/robotic-process/errors", "GET /visa/robotic-process/list", "PUT /visa/robotic-process/.*/status/toggle", "PUT /visa/robotic-process/.*/status/toggle", "GET /visa/robotic-process/errors/.*/exclude", "PUT /visa/robotic-process/status/toggle", "POST /visa/rpa-config/updateAll", "GET /visa/rpa-config/getDDWorkingHour"], "visa_RPAErrorsReport": ["POST /visa/robotic-process/errors", "POST /visa/robotic-process/errors/.*/dismiss", "GET /visa/robotic-process/errors/export/excel"], "accounting_vat-app-sign-dd": ["GET /accounting/contractpaymentterm/getddinfoforclient", "POST /accounting/contractpaymentterm/signddbyclient", "POST /accounting/contractpaymentterm/getnamefromeidphoto", "GET /accounting/directDebit/getDDFormExample"], "accounting_vat-app-sign-dd-v2": ["GET /accounting/contractpaymentterm/getddinfoforclient", "POST /accounting/contractpaymentterm/signddbyclient", "POST /accounting/contractpaymentterm/getnamefromeidphoto"], "accounting_sign-spouse": ["POST /accounting/contractpaymentterm/erp/switchingBankAccount/doSwitch"], "ACCOUNTING__visaExpensesStatements": ["GET /accounting/visaStatement/allStatements", "POST /accounting/visaStatement/startParsing", "DELETE /accounting/visaStatement/delete/", "GET /public/download/.*"], "ACCOUNTING__visaExpensesStatementMatchingResult": ["GET /accounting/visaStatement/", "GET /accounting/visaStatementTransaction/getMatchedTransaction/", "GET /accounting/visaStatementTransaction/wrongMatch/", "POST /accounting/visaStatementTransaction/confirmSelected", "POST /accounting/visaStatementTransaction/confirm/", "POST /accounting/visaStatementTransaction/rematch", "POST /accounting/visaStatementTransaction/dismiss", "POST /accounting/visaStatementTransaction/fixSelectedERPAmount", "GET /accounting/buckets/page/searchBuckets", "GET /accounting/expenses/page/searchExpenses", "GET /accounting/visaStatementTransaction/getMissingFromERPTransaction/", "GET /accounting/visaStatementTransaction/getMissingFromStatementTransaction/", "GET /accounting/visaStatementTransaction/getSameReferenceNumberButDifferentAmountTransaction/", "GET /accounting/visaStatement/canBeConfirm/", "GET /accounting/visaStatement/confirm/", "GET /public/download/.*", "GET /accounting/visaStatementTransaction/confirmAll/", "GET /accounting/visaStatementTransaction/rematchAll/", "POST /accounting/visaStatementTransaction/updateAll"], "accounting_BankTransferApproval": ["POST /accounting/bankTransfers/saveTask/.*", "POST /accounting/bankTransfers/complete/.*", "GET /accounting/workflowTasks/lazyLoad", "GET /visa/workflowTasks/getAllSearchbleFields", "POST /accounting/workflowTasks/all"], "Company'sContractAndAgreements": ["POST /accounting/tenancyContracts/advancedSearch/page", "DELETE /accounting/tenancyContracts/delete/", "POST /accounting/tenancyContracts/update"], "Company'sContractAndAgreementsDetails": ["GET /accounting/tenancyContracts/", "GET /accounting/nonclientpdc/byTenancyContract/", "POST /accounting/tenancyContracts/update", "GET /admin/user/page/active", "POST /accounting/tenancyContracts/create"], "ACCOUNTING__CollectionEventsConfig": ["GET /accounting/flowProcessor/page", "GET /admin/tag/list", "POST /accounting/flowProcessor/update", "GET /admin/tag/page", "GET /admin/tag/create.*", "GET /admin/picklist/items/ReasonOfTermination", "POST /accounting/flowProcessor/update", "GET /accounting/flowSubEvents/requiredDocuments", "GET /accounting/flowSubEvents/requiredActions", "GET /accounting/flowProcessor/subEventConfigs/", "POST /accounting/flowSubEvents/update", "DELETE /accounting/flowSubEvents/delete/", "POST /accounting/flowProcessor/savePeriods/"], "ExpenseRefundsHistory": ["POST /accounting/expenseRequestTodo/searchRefund", "POST /accounting/expenseRequestTodo/update", "POST /accounting/expenseRequestTodo/exportRefundsAsCsv", "GET /accounting/supplier/get-supplier-list", "GET /public/picklist/items/.*"], "accounting__public_signature_approval_link": ["POST /accounting/contractpaymentterm/saveClientSignatures", "GET /accounting/directDebitSignature/isGenerateSignatureApprovalLinkExpired", "GET /accounting/contract/getContractPreviewFiles"], "accounting_credit-card-statements": ["POST /accounting/onlineCardStatementFile/create", "POST /accounting/onlineCardStatementFile/.*", "GET /accounting/onlineCardStatementFile/deleteOnlineCardStatementFile/", "GET /accounting/onlineCardStatementFile/onlineCardStatementFileSearch", "GET /public/download/.*"], "accounting_credit-card-statement-result": ["GET /accounting/onlineCardStatementFile/", "GET /public/picklist/items/.*", "GET /accounting/onlineCardStatementRecord/confirmTodoManually/", "GET /accounting/onlineCardStatementRecord/viewResult/", "GET /accounting/onlineCardStatementRecord/searchByGrid/", "GET /accounting/onlineCardStatementRecord/getTodosForMatchOnlineStatement/", "GET /accounting/onlineCardStatementRecord/generateCsv/", "POST /accounting/onlineCardStatementFile/create", "GET /accounting/onlineCardStatementFile/createPosTransaction/.*", "GET /accounting/onlineCardStatementRecord/getTodosForMatchOnlineStatementForRefundPayment/", "GET /public/download/.*"], "ACCOUNTING__AuditActiveEmployees": ["GET /accounting/auditActiveEmployeesFile/", "POST /accounting/auditActiveEmployeesFile/startParsing", "DELETE /accounting/auditActiveEmployeesFile/delete/"], "ACCOUNTING__AuditActiveEmployeesResult": ["GET /accounting/auditActiveEmployeesFile/", "GET /accounting/auditActiveEmployeeRecord/getActiveOnERPButNotOnFile/", "GET /accounting/auditActiveEmployeeRecord/getActiveOnFileButNotOnERP/", "POST /accounting/onlineCardStatementFile/.*", "GET /accounting/onlineCardStatementRecord/confirmTodoManually/.*", "GET /accounting/onlineCardStatementRecord/viewResult/", "GET /accounting/onlineCardStatementRecord/searchByGrid/", "GET /accounting/onlineCardStatementRecord/getTodosForMatchOnlineStatement/", "POST /accounting/onlineCardStatementFile/.*", "GET /public/picklist/items/.*", "GET /accounting/onlineCardStatementRecord/confirmTodoManually/", "GET /accounting/onlineCardStatementRecord/viewResult/", "GET /accounting/onlineCardStatementRecord/searchByGrid/", "GET /accounting/onlineCardStatementRecord/getTodosForMatchOnlineStatement/", "GET /accounting/onlineCardStatementRecord/generateCsv/", "POST /accounting/onlineCardStatementFile/create", "GET /public/download/.*", "POST /accounting/onlineCardStatementFile/create", "GET /accounting/auditActiveEmployeesFile/exportToCsv/"], "AccountingCashierScheduleCollection": ["GET /accounting/expense-payment/getCashierScheduleCollectionLinkInfo/", "GET /accounting/expense-payment/cashiersSendCollectionRequest/"], "ACCOUNTING__recurringCCPaymentsIssuesMgmt": ["GET /accounting/recurringCreditCardPaymentsIssue/page", "GET /accounting/recurringCreditCardPaymentsIssue/getTheErrorCodeNotSelectedAndTheSubFlow", "POST /accounting/recurringCreditCardPaymentsIssue/create", "POST /accounting/recurringCreditCardPaymentsIssue/createAll", "POST /accounting/recurringCreditCardPaymentsIssue/updateAll", "DELETE /accounting/recurringCreditCardPaymentsIssue/delete/.*"]}